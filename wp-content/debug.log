[01-Jul-2025 12:01:46 UTC] FINAL RETURN DATA: {"success":true,"message":"Upload completed. Success: 0, Failed: 2","results":[{"success":false,"unique_id":"file_0_1751371305_8984","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai.png","filename":"ban-chai.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">9585c0345e39e2f0<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":0,"group_size":2},{"success":false,"unique_id":"file_1_1751371305_7767","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai_1.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1.png","filename":"ban-chai_1.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">9585c0351f78f548<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":0,"group_size":2}],"successful_uploads":[],"failed_uploads":[{"success":false,"unique_id":"file_0_1751371305_8984","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai.png","filename":"ban-chai.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">9585c0345e39e2f0<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":0,"group_size":2},{"success":false,"unique_id":"file_1_1751371305_7767","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai_1.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1.png","filename":"ban-chai_1.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">9585c0351f78f548<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":0,"group_size":2}],"summary":{"total":2,"successful":0,"failed":2}}
[01-Jul-2025 15:47:25 UTC] PHP Warning:  require_once(C:\laragon\www\reup\wp-content\plugins\huong-dan/includes/class-admin.php): Failed to open stream: No such file or directory in C:\laragon\www\reup\wp-content\plugins\huong-dan\huong-dan.php on line 22
[01-Jul-2025 15:47:25 UTC] PHP Fatal error:  Uncaught Error: Failed opening required 'C:\laragon\www\reup\wp-content\plugins\huong-dan/includes/class-admin.php' (include_path='.;C:/laragon/etc/php/pear') in C:\laragon\www\reup\wp-content\plugins\huong-dan\huong-dan.php:22
Stack trace:
#0 C:\laragon\www\reup\wp-admin\includes\plugin.php(2387): include_once()
#1 C:\laragon\www\reup\wp-admin\includes\plugin.php(673): plugin_sandbox_scrape('huong-dan/huong...')
#2 C:\laragon\www\reup\wp-admin\plugins.php(60): activate_plugin('huong-dan/huong...', 'http://reup.tes...', false)
#3 {main}
  thrown in C:\laragon\www\reup\wp-content\plugins\huong-dan\huong-dan.php on line 22
[01-Jul-2025 15:48:00 UTC] PHP Warning:  require_once(C:\laragon\www\reup\wp-content\plugins\huong-dan/includes/class-admin.php): Failed to open stream: No such file or directory in C:\laragon\www\reup\wp-content\plugins\huong-dan\huong-dan.php on line 22
[01-Jul-2025 15:48:00 UTC] PHP Fatal error:  Uncaught Error: Failed opening required 'C:\laragon\www\reup\wp-content\plugins\huong-dan/includes/class-admin.php' (include_path='.;C:/laragon/etc/php/pear') in C:\laragon\www\reup\wp-content\plugins\huong-dan\huong-dan.php:22
Stack trace:
#0 C:\laragon\www\reup\wp-admin\includes\plugin.php(2387): include_once()
#1 C:\laragon\www\reup\wp-admin\includes\plugin.php(673): plugin_sandbox_scrape('huong-dan/huong...')
#2 C:\laragon\www\reup\wp-admin\plugins.php(60): activate_plugin('huong-dan/huong...', 'http://reup.tes...', false)
#3 {main}
  thrown in C:\laragon\www\reup\wp-content\plugins\huong-dan\huong-dan.php on line 22
[01-Jul-2025 15:52:18 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:52:18 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 15:52:20 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:52:20 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC made by do_action('huong-dan_page_huong-dan-menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->menu_manager, HuongDan_Database::get_hierarchical_pages
[01-Jul-2025 15:52:22 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:52:56 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:52:56 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 15:52:59 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:52:59 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC made by do_action('huong-dan_page_huong-dan-menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->menu_manager, HuongDan_Database::get_hierarchical_pages
[01-Jul-2025 15:53:02 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:53:18 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:53:18 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:53:19 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:53:34 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:53:34 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC made by do_action('huong-dan_page_huong-dan-menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->menu_manager, HuongDan_Database::get_hierarchical_pages
[01-Jul-2025 15:53:36 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:53:36 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 15:53:38 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:17 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:17 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('huong-dan_page_huong-dan-new'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->new_page, HuongDan_Admin->render_page_form, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:37 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:37 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:37 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:39 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:39 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:41 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:41 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:41 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:41 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('huong-dan_page_huong-dan-new'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->new_page, HuongDan_Admin->render_page_form, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:42 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:42 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC made by do_action('huong-dan_page_huong-dan-menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->menu_manager, HuongDan_Database::get_hierarchical_pages
[01-Jul-2025 15:55:44 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:55:44 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 15:57:02 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:57:02 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 15:57:05 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:57:05 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('huong-dan_page_huong-dan-new'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->new_page, HuongDan_Admin->render_page_form, HuongDan_Database::get_all_pages
[01-Jul-2025 15:57:15 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:57:15 UTC] WordPress database error Unknown column 'parent_id' in 'field list' for query INSERT INTO `wp_huong_dan_pages` (`slug`, `title`, `content`, `parent_id`, `menu_order`, `depth`, `is_parent`, `has_link`, `icon`) VALUES ('huong-dan', 'huong da', '', 0, 0, 0, 1, 0, '') made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_init, HuongDan_Admin->save_page, HuongDan_Database::create_page
[01-Jul-2025 15:57:15 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by require('wp-admin/menu.php'), require_once('wp-admin/includes/menu.php'), do_action('admin_menu'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->add_admin_menu, HuongDan_Database::get_all_pages
[01-Jul-2025 15:57:15 UTC] WordPress database error Unknown column 'parent_id' in 'order clause' for query SELECT * FROM wp_huong_dan_pages WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC made by do_action('toplevel_page_huong-dan'), WP_Hook->do_action, WP_Hook->apply_filters, HuongDan_Admin->admin_page, HuongDan_Database::get_all_pages
[01-Jul-2025 16:00:50 UTC] Huong Dan Plugin: Table creation result: Array
(
)

[01-Jul-2025 16:02:57 UTC] Huong Dan Plugin: Table creation result: Array
(
)

[01-Jul-2025 16:08:56 UTC] PHP Fatal error:  Uncaught TypeError: call_user_func_array(): Argument #1 ($callback) must be a valid callback, class HuongDan_Frontend does not have a method "maybe_flush_rewrite_rules" in C:\laragon\www\reup\wp-includes\class-wp-hook.php:324
Stack trace:
#0 C:\laragon\www\reup\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#1 C:\laragon\www\reup\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#2 C:\laragon\www\reup\wp-settings.php(749): do_action('wp_loaded')
#3 C:\laragon\www\reup\wp-config.php(105): require_once('C:\\laragon\\www\\...')
#4 C:\laragon\www\reup\wp-load.php(50): require_once('C:\\laragon\\www\\...')
#5 C:\laragon\www\reup\wp-admin\admin-ajax.php(22): require_once('C:\\laragon\\www\\...')
#6 {main}
  thrown in C:\laragon\www\reup\wp-includes\class-wp-hook.php on line 324
[01-Jul-2025 16:10:00 UTC] Huong Dan Plugin: Table creation result: Array
(
)

[01-Jul-2025 16:11:08 UTC] Huong Dan Plugin: Table creation result: Array
(
)

[01-Jul-2025 16:55:24 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:55:25 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:55:26 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:55:26 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:55:26 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:55:26 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:55:26 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:55:27 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:55:28 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:56:41 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:56:41 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[01-Jul-2025 16:56:42 UTC] PHP Fatal error:  Cannot redeclare HuongDan_Frontend::render_sidebar_menu() in C:\laragon\www\reup\wp-content\plugins\huong-dan\includes\class-frontend.php on line 249
[02-Jul-2025 02:48:00 UTC] Automatic updates starting...
[02-Jul-2025 02:48:00 UTC]   Automatic plugin updates starting...
[02-Jul-2025 02:48:00 UTC]   Automatic plugin updates complete.
[02-Jul-2025 02:48:01 UTC]   Automatic theme updates starting...
[02-Jul-2025 02:48:01 UTC]   Automatic theme updates complete.
[02-Jul-2025 02:48:01 UTC] Automatic updates complete.
[02-Jul-2025 13:45:25 UTC] Automatic updates starting...
[02-Jul-2025 13:45:27 UTC]   Automatic plugin updates starting...
[02-Jul-2025 13:45:27 UTC]   Automatic plugin updates complete.
[02-Jul-2025 13:45:27 UTC]   Automatic theme updates starting...
[02-Jul-2025 13:45:27 UTC]   Automatic theme updates complete.
[02-Jul-2025 13:45:27 UTC] Automatic updates complete.
[02-Jul-2025 13:52:36 UTC] FINAL RETURN DATA: {"success":true,"message":"Upload completed. Success: 0, Failed: 4","results":[{"success":false,"unique_id":"file_2_1751464355_5038","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai-k51-3377.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai-k51-3377.png","filename":"ban-chai-k51-3377.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">958e9fecaf7efde6<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":0,"group_size":1},{"success":false,"unique_id":"file_3_1751464355_9999","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai-k514-5578.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai-k514-5578.png","filename":"ban-chai-k514-5578.png","domain":"https:\/\/hentai-sub.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> hentai-sub.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">958e9fedbfc072da<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":1,"group_size":1},{"success":false,"unique_id":"file_0_1751464355_2275","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai.png","filename":"ban-chai.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">958e9fee7e2e8348<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":2,"group_size":2},{"success":false,"unique_id":"file_1_1751464355_8131","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai_1.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1.png","filename":"ban-chai_1.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">958e9fef49ac6061<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":2,"group_size":2}],"successful_uploads":[],"failed_uploads":[{"success":false,"unique_id":"file_2_1751464355_5038","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai-k51-3377.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai-k51-3377.png","filename":"ban-chai-k51-3377.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">958e9fecaf7efde6<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":0,"group_size":1},{"success":false,"unique_id":"file_3_1751464355_9999","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai-k514-5578.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai-k514-5578.png","filename":"ban-chai-k514-5578.png","domain":"https:\/\/hentai-sub.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> hentai-sub.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">958e9fedbfc072da<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":1,"group_size":1},{"success":false,"unique_id":"file_0_1751464355_2275","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai.png","filename":"ban-chai.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">958e9fee7e2e8348<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":2,"group_size":2},{"success":false,"unique_id":"file_1_1751464355_8131","original_path":"C:\\laragon\\www\\reup\/wp-content\/uploads\/aui-temp\/ban-chai_1.png","original_url":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1.png","filename":"ban-chai_1.png","domain":"https:\/\/cn-av.com","error":"HTTP 403: <!DOCTYPE html>\n<!--[if lt IE 7]> <html class=\"no-js ie6 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 7]>    <html class=\"no-js ie7 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if IE 8]>    <html class=\"no-js ie8 oldie\" lang=\"en-US\"> <![endif]-->\n<!--[if gt IE 8]><!--> <html class=\"no-js\" lang=\"en-US\"> <!--<![endif]-->\n<head>\n<title>Attention Required! | Cloudflare<\/title>\n<meta charset=\"UTF-8\" \/>\n<meta http-equiv=\"Content-Type\" content=\"text\/html; charset=UTF-8\" \/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\" \/>\n<meta name=\"robots\" content=\"noindex, nofollow\" \/>\n<meta name=\"viewport\" content=\"width=device-width,initial-scale=1\" \/>\n<link rel=\"stylesheet\" id=\"cf_styles-css\" href=\"\/cdn-cgi\/styles\/cf.errors.css\" \/>\n<!--[if lt IE 9]><link rel=\"stylesheet\" id='cf_styles-ie-css' href=\"\/cdn-cgi\/styles\/cf.errors.ie.css\" \/><![endif]-->\n<style>body{margin:0;padding:0}<\/style>\n\n\n<!--[if gte IE 10]><!-->\n<script>\n  if (!navigator.cookieEnabled) {\n    window.addEventListener('DOMContentLoaded', function () {\n      var cookieEl = document.getElementById('cookie-alert');\n      cookieEl.style.display = 'block';\n    })\n  }\n<\/script>\n<!--<![endif]-->\n\n<\/head>\n<body>\n  <div id=\"cf-wrapper\">\n    <div class=\"cf-alert cf-alert-error cf-cookie-error\" id=\"cookie-alert\" data-translate=\"enable_cookies\">Please enable cookies.<\/div>\n    <div id=\"cf-error-details\" class=\"cf-error-details-wrapper\">\n      <div class=\"cf-wrapper cf-header cf-error-overview\">\n        <h1 data-translate=\"block_headline\">Sorry, you have been blocked<\/h1>\n        <h2 class=\"cf-subheadline\"><span data-translate=\"unable_to_access\">You are unable to access<\/span> cn-av.com<\/h2>\n      <\/div><!-- \/.header -->\n\n      <div class=\"cf-section cf-highlight\">\n        <div class=\"cf-wrapper\">\n          <div class=\"cf-screenshot-container cf-screenshot-full\">\n            \n              <span class=\"cf-no-screenshot error\"><\/span>\n            \n          <\/div>\n        <\/div>\n      <\/div><!-- \/.captcha-container -->\n\n      <div class=\"cf-section cf-wrapper\">\n        <div class=\"cf-columns two\">\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_why_headline\">Why have I been blocked?<\/h2>\n\n            <p data-translate=\"blocked_why_detail\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.<\/p>\n          <\/div>\n\n          <div class=\"cf-column\">\n            <h2 data-translate=\"blocked_resolve_headline\">What can I do to resolve this?<\/h2>\n\n            <p data-translate=\"blocked_resolve_detail\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.<\/p>\n          <\/div>\n        <\/div>\n      <\/div><!-- \/.section -->\n\n      <div class=\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\">\n    <p class=\"text-13\">\n      <span class=\"cf-footer-item sm:block sm:mb-1\">Cloudflare Ray ID: <strong class=\"font-semibold\">958e9fef49ac6061<\/strong><\/span>\n      <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <span id=\"cf-footer-item-ip\" class=\"cf-footer-item hidden sm:block sm:mb-1\">\n        Your IP:\n        <button type=\"button\" id=\"cf-footer-ip-reveal\" class=\"cf-footer-ip-reveal-btn\">Click to reveal<\/button>\n        <span class=\"hidden\" id=\"cf-footer-ip\">*************<\/span>\n        <span class=\"cf-footer-separator sm:hidden\">&bull;<\/span>\n      <\/span>\n      <span class=\"cf-footer-item sm:block sm:mb-1\"><span>Performance &amp; security by<\/span> <a rel=\"noopener noreferrer\" href=\"https:\/\/www.cloudflare.com\/5xx-error-landing\" id=\"brand_link\" target=\"_blank\">Cloudflare<\/a><\/span>\n      \n    <\/p>\n    <script>(function(){function d(){var b=a.getElementById(\"cf-footer-item-ip\"),c=a.getElementById(\"cf-footer-ip-reveal\");b&&\"classList\"in b&&(b.classList.remove(\"hidden\"),c.addEventListener(\"click\",function(){c.classList.add(\"hidden\");a.getElementById(\"cf-footer-ip\").classList.remove(\"hidden\")}))}var a=document;document.addEventListener&&a.addEventListener(\"DOMContentLoaded\",d)})();<\/script>\n  <\/div><!-- \/.error-footer -->\n\n    <\/div><!-- \/#cf-error-details -->\n  <\/div><!-- \/#cf-wrapper -->\n\n  <script>\n    window._cf_translation = {};\n    \n    \n  <\/script>\n<\/body>\n<\/html>","group_index":2,"group_size":2}],"summary":{"total":4,"successful":0,"failed":4}}
