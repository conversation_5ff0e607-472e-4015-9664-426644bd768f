<?php
/**
 * Admin Page Class
 * Create admin pages for the plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class AUI_Admin_Page {
    
    private $config_manager;
    private $image_uploader;
    
    /**
     * Constructor
     * @param AUI_Config_Manager $config_manager
     * @param AUI_Image_Uploader $image_uploader
     */
    public function __construct($config_manager, $image_uploader) {
        $this->config_manager = $config_manager;
        $this->image_uploader = $image_uploader;
        
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'handle_form_submissions'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Show menu to users with 'read' capability (includes admin and subscriber)
        if (!current_user_can('read')) {
            return;
        }

        add_menu_page(
            'Auto Upload Image',
            'Auto Upload Image',
            'read',
            'auto-upload-image',
            array($this, 'render_main_page'),
            'dashicons-upload',
            30
        );

        // Only show config page to administrators
        if (current_user_can('administrator')) {
            add_submenu_page(
                'auto-upload-image',
                'API Configuration',
                'API Configuration',
                'administrator',
                'auto-upload-image-config',
                array($this, 'render_config_page')
            );
        }
    }
    
    /**
     * Handle form submissions
     */
    public function handle_form_submissions() {
        // Only administrators can modify configuration
        if (!current_user_can('administrator')) {
            return;
        }
        
        // Handle domain configuration
        if (isset($_POST['aui_save_domains']) && wp_verify_nonce($_POST['aui_nonce'], 'aui_save_domains')) {
            $domains = isset($_POST['domains']) ? $_POST['domains'] : array();
            $domains = array_filter(array_map('trim', $domains));

            if ($this->config_manager->update_domains($domains)) {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-success"><p>Domain configuration saved successfully!</p></div>';
                });
            } else {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-error"><p>Error saving domain configuration!</p></div>';
                });
            }
        }
        
        // Handle settings
        if (isset($_POST['aui_save_settings']) && wp_verify_nonce($_POST['aui_nonce'], 'aui_save_settings')) {
            $settings = array(
                'retry_attempts' => intval($_POST['retry_attempts']),
                'delay_seconds' => intval($_POST['delay_seconds']),
                'auto_delete' => isset($_POST['auto_delete']) ? 1 : 0
            );
            
            if ($this->config_manager->update_settings($settings)) {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-success"><p>Settings saved successfully!</p></div>';
                });
            } else {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-error"><p>Error saving settings!</p></div>';
                });
            }
        }
    }
    
    /**
     * Render main page
     */
    public function render_main_page() {
        $domains = $this->config_manager->get_domains();
        $settings = $this->config_manager->get_settings();
        ?>
        <div class="wrap">
            <h1>Auto Upload Image</h1>
            
            <?php if (empty($domains)): ?>
                <div class="notice notice-warning">
                    <p>No API domains configured yet. <a href="<?php echo admin_url('admin.php?page=auto-upload-image-config'); ?>">Configure now</a></p>
                </div>
            <?php endif; ?>

            <div class="aui-upload-section">
                <h2>Upload Images</h2>
                <p>Select images from your computer to upload:</p>

                <form id="aui-upload-form" enctype="multipart/form-data">
                    <div class="aui-file-upload-area">
                        <input type="file" id="aui-image-files" name="image_files[]" multiple accept="image/*" style="display: none;">
                        <div id="aui-drop-zone" class="aui-drop-zone">
                            <div class="aui-drop-zone-content">
                                <span class="dashicons dashicons-cloud-upload"></span>
                                <p>Drag and drop images here or <a href="#" id="aui-browse-files">browse files</a></p>
                                <p class="description">Supported: JPG, PNG, GIF, WEBP</p>
                            </div>
                        </div>
                        <div id="aui-selected-files" class="aui-selected-files"></div>
                    </div>
                    <br>
                    <div class="aui-upload-options">
                        <label>
                            <input type="checkbox" id="aui-auto-remove-uploaded" checked>
                            Automatically remove files from list after successful upload
                        </label>
                    </div>
                    <br>
                    <button type="submit" class="button button-primary" disabled>Upload Images</button>
                    <button type="button" id="aui-clear-files" class="button button-secondary">Clear All</button>
                    <span id="aui-upload-status"></span>
                </form>

                <div id="aui-upload-results" style="display: none;">
                    <h3>Upload Results</h3>
                    <div id="aui-results-content"></div>

                    <!-- Test All Links section -->
                    <div id="aui-test-all-links" style="display: none;">
                        <div class="aui-all-links">
                            <h4>📋 All Links (Test)</h4>
                            <div class="aui-links-container">
                                <textarea id="aui-all-links-textarea" readonly placeholder="Link list will be displayed here...">https://example.com/test1.jpg
https://example.com/test2.jpg</textarea>
                                <div class="aui-links-actions">
                                    <button id="aui-copy-all-links" class="button button-primary" title="Copy all links to clipboard">📋 Copy All</button>
                                    <button id="aui-select-all-links" class="button button-secondary" title="Select all text">🔍 Select All</button>
                                    <span class="description">Click on textarea to select all links</span>
                                </div>
                            </div>
                        </div>
                    </div>



                </div>
            </div>
            
            <div class="aui-info-section">
                <h3>Current Configuration Info</h3>
                <table class="widefat">
                    <tr>
                        <td><strong>Number of API domains:</strong></td>
                        <td><?php echo count($domains); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Domains:</strong></td>
                        <td>
                            <?php if (!empty($domains)): ?>
                                <ul>
                                    <?php foreach ($domains as $domain): ?>
                                        <li><?php echo esc_html($domain); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php else: ?>
                                <em>Not configured</em>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Retry attempts:</strong></td>
                        <td><?php echo $settings['retry_attempts']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Upload delay:</strong></td>
                        <td><?php echo $settings['delay_seconds']; ?> seconds</td>
                    </tr>
                    <tr>
                        <td><strong>Auto delete files:</strong></td>
                        <td><?php echo $settings['auto_delete'] ? 'Yes' : 'No'; ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <style>
        .aui-upload-section {
            background: #fff;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ccd0d4;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        .aui-info-section {
            background: #fff;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ccd0d4;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        #aui-image-urls {
            width: 100%;
            max-width: 800px;
        }
        .aui-result-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .aui-result-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .aui-result-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .aui-result-links {
            margin-top: 10px;
        }
        .aui-result-links a {
            margin-right: 10px;
            text-decoration: none;
        }
        #aui-upload-status {
            margin-left: 10px;
            font-weight: bold;
        }
        .aui-loading {
            color: #0073aa;
        }
        .aui-success {
            color: #46b450;
        }
        .aui-error {
            color: #dc3232;
        }
        </style>
        <?php
    }
    
    /**
     * Render config page
     */
    public function render_config_page() {
        // Only administrators can access config page
        if (!current_user_can('administrator')) {
            wp_die('You do not have permission to access this page.');
        }

        $domains = $this->config_manager->get_domains();
        $settings = $this->config_manager->get_settings();
        ?>
        <div class="wrap">
            <h1>Auto Upload Image Configuration</h1>

            <form method="post">
                <?php wp_nonce_field('aui_save_domains', 'aui_nonce'); ?>

                <h2>API Domain Configuration</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">API Domains</th>
                        <td>
                            <div id="aui-domains-list">
                                <?php if (!empty($domains)): ?>
                                    <?php foreach ($domains as $index => $domain): ?>
                                        <div class="aui-domain-item">
                                            <input type="url" name="domains[]" value="<?php echo esc_attr($domain); ?>" style="width: 400px;" />
                                            <button type="button" class="button aui-remove-domain">Remove</button>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="aui-domain-item">
                                        <input type="url" name="domains[]" value="" placeholder="https://example.com" style="width: 400px;" />
                                        <button type="button" class="button aui-remove-domain">Remove</button>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <br>
                            <button type="button" id="aui-add-domain" class="button">Add Domain</button>
                            <p class="description">Enter full URL of API domains (example: https://cn-av.com)</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="aui_save_domains" class="button-primary" value="Save Domains" />
                </p>
            </form>

            <hr>

            <form method="post">
                <?php wp_nonce_field('aui_save_settings', 'aui_nonce'); ?>

                <h2>Upload Settings</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">Retry Attempts</th>
                        <td>
                            <input type="number" name="retry_attempts" value="<?php echo esc_attr($settings['retry_attempts']); ?>" min="0" max="10" />
                            <p class="description">Number of retry attempts when upload fails (0-10)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Upload Delay</th>
                        <td>
                            <input type="number" name="delay_seconds" value="<?php echo esc_attr($settings['delay_seconds']); ?>" min="0" max="60" />
                            <p class="description">Delay in seconds between uploads to same domain (0-60)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Auto Delete Files</th>
                        <td>
                            <label>
                                <input type="checkbox" name="auto_delete" value="1" <?php checked($settings['auto_delete'], 1); ?> />
                                Automatically delete local files after successful upload
                            </label>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <input type="submit" name="aui_save_settings" class="button-primary" value="Save Settings" />
                </p>
            </form>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Add domain
            $('#aui-add-domain').click(function() {
                var newDomain = '<div class="aui-domain-item">' +
                    '<input type="url" name="domains[]" value="" placeholder="https://example.com" style="width: 400px;" />' +
                    '<button type="button" class="button aui-remove-domain">Remove</button>' +
                    '</div>';
                $('#aui-domains-list').append(newDomain);
            });
            
            // Remove domain
            $(document).on('click', '.aui-remove-domain', function() {
                if ($('.aui-domain-item').length > 1) {
                    $(this).parent().remove();
                }
            });
        });
        </script>
        
        <style>
        .aui-domain-item {
            margin-bottom: 10px;
        }
        .aui-domain-item input {
            margin-right: 10px;
        }
        </style>
        <?php
    }
}
