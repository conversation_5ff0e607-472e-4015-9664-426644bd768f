/**
 * Auto Upload Image Admin JavaScript
 */

jQuery(document).ready(function($) {

    var uploadInProgress = false;
    var uploadResults = [];
    var successfulFiles = [];
    var selectedFiles = [];

    /**
     * Initialize file upload functionality
     */
    initFileUpload();

    /**
     * Initialize file upload drag & drop
     */
    function initFileUpload() {
        var $dropZone = $('#aui-drop-zone');
        var $fileInput = $('#aui-image-files');
        var $browseLink = $('#aui-browse-files');
        var $submitBtn = $('button[type="submit"]');

        // Browse files click
        $browseLink.on('click', function(e) {
            e.preventDefault();
            $fileInput.click();
        });

        // Drop zone click
        $dropZone.on('click', function() {
            $fileInput.click();
        });

        // File input change
        $fileInput.on('change', function() {
            handleFiles(this.files);
        });

        // Drag & drop events
        $dropZone.on('dragover dragenter', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('dragover');
        });

        $dropZone.on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');
        });

        $dropZone.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');

            var files = e.originalEvent.dataTransfer.files;
            handleFiles(files);
        });

        // Clear files button
        $('#aui-clear-files').on('click', function() {
            clearSelectedFiles();
        });

        // Test button for All Links
        $('#aui-show-test-links').on('click', function() {
            $('#aui-test-all-links').toggle();
            setupCopyHandlers();
        });
    }

    /**
     * Handle selected files
     */
    function handleFiles(files) {
        for (var i = 0; i < files.length; i++) {
            var file = files[i];

            // Check if it's an image
            if (!file.type.match('image.*')) {
                alert('File "' + file.name + '" is not an image!');
                continue;
            }

            // Check file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('File "' + file.name + '" is too large (max 10MB)!');
                continue;
            }

            // Check if already selected
            var exists = selectedFiles.some(function(f) {
                return f.name === file.name && f.size === file.size;
            });

            if (!exists) {
                console.log('AUI Debug - Adding new file:', file.name, 'size:', file.size);
                selectedFiles.push(file);
            } else {
                console.log('AUI Debug - File already exists, skipping:', file.name, 'size:', file.size);
            }
        }

        updateFileList();
        updateSubmitButton();
    }

    /**
     * Update file list display
     */
    function updateFileList() {
        var $container = $('#aui-selected-files');
        $container.empty();

        selectedFiles.forEach(function(file, index) {
            var $item = $('<div class="aui-file-item">');
            $item.attr('data-filename', file.name); // Add data attribute for identification

            // Create preview
            var $preview = $('<img class="aui-file-preview">');
            var reader = new FileReader();
            reader.onload = function(e) {
                $preview.attr('src', e.target.result);
            };
            reader.readAsDataURL(file);

            // File info
            var $info = $('<div class="aui-file-info">');
            $info.append('<div class="aui-file-name">' + file.name + '</div>');
            $info.append('<div class="aui-file-size">' + formatFileSize(file.size) + '</div>');

            // Remove button
            var $remove = $('<button class="aui-file-remove" data-index="' + index + '">Xóa</button>');
            $remove.on('click', function() {
                removeFile($(this).data('index'));
            });

            $item.append($preview).append($info).append($remove);
            $container.append($item);
        });
    }

    /**
     * Remove files with animation
     */
    function removeFilesWithAnimation(filenames, callback) {
        if (!filenames || filenames.length === 0) {
            if (callback) callback();
            return;
        }

        var $itemsToRemove = [];
        filenames.forEach(function(filename) {
            var $item = $('.aui-file-item[data-filename="' + filename + '"]');
            if ($item.length > 0) {
                $itemsToRemove.push($item);
            }
        });

        if ($itemsToRemove.length === 0) {
            if (callback) callback();
            return;
        }

        // Add removing class for animation
        $itemsToRemove.forEach(function($item) {
            $item.addClass('removing');
        });

        // Wait for animation to complete, then update the list
        setTimeout(function() {
            if (callback) callback();
        }, 300);
    }

    /**
     * Remove file from selection
     */
    function removeFile(index) {
        selectedFiles.splice(index, 1);
        updateFileList();
        updateSubmitButton();
    }

    /**
     * Clear all selected files
     */
    function clearSelectedFiles() {
        selectedFiles = [];
        $('#aui-selected-files').empty();
        $('#aui-image-files').val('');
        updateSubmitButton();
    }

    /**
     * Update submit button state
     */
    function updateSubmitButton() {
        var $submitBtn = $('button[type="submit"]');
        if (selectedFiles.length > 0) {
            $submitBtn.prop('disabled', false).text('Upload ' + selectedFiles.length + ' images');
        } else {
            $submitBtn.prop('disabled', true).text('Upload Images');
        }
    }

    /**
     * Remove successfully uploaded files from selection
     */
    function removeUploadedFilesFromSelection(results) {
        if (!results || results.length === 0) {
            return;
        }

        var successfulFilenames = [];
        results.forEach(function(result) {
            if (result.success && result.filename) {
                successfulFilenames.push(result.filename);
            }
        });

        console.log('Files to remove from selection:', successfulFilenames);

        if (successfulFilenames.length === 0) {
            return;
        }

        // Animate removal first
        removeFilesWithAnimation(successfulFilenames, function() {
            var removedCount = 0;

            // Remove successful files from selectedFiles array
            selectedFiles = selectedFiles.filter(function(file) {
                var shouldKeep = !successfulFilenames.includes(file.name);
                if (!shouldKeep) {
                    console.log('Removing file from selection:', file.name);
                    removedCount++;
                }
                return shouldKeep;
            });

            console.log('Remaining selected files:', selectedFiles.length);
            console.log('Removed files count:', removedCount);

            // Update UI
            updateFileList();
            updateSubmitButton();

            // Show notification
            if (removedCount > 0) {
                var message = '';
                if (selectedFiles.length === 0) {
                    message = '✓ All ' + removedCount + ' files uploaded successfully and removed from list!';
                } else {
                    message = '✓ ' + removedCount + ' files uploaded successfully and removed from list. ' + selectedFiles.length + ' files remaining.';
                }

                setTimeout(function() {
                    $('#aui-upload-status').html('<span class="aui-success">' + message + '</span>');
                }, 500);
            }
        });
    }

    /**
     * Handle upload form submission
     */
    $('#aui-upload-form').on('submit', function(e) {
        e.preventDefault();

        if (uploadInProgress) {
            return;
        }

        if (selectedFiles.length === 0) {
            alert('Please select at least one image!');
            return;
        }

        startUpload();
    });
    
    /**
     * Start upload process
     */
    function startUpload() {
        uploadInProgress = true;
        uploadResults = [];
        successfulFiles = [];

        // Update UI
        $('#aui-upload-status').html('<span class="aui-loading">Uploading ' + selectedFiles.length + ' images...</span>');
        $('button[type="submit"]').prop('disabled', true).addClass('loading');
        $('#aui-upload-results').hide();

        // Show progress
        showProgress(0, selectedFiles.length);

        // Start progress simulation
        var progressInterval = startProgressSimulation(selectedFiles.length);

        // Create FormData
        var formData = new FormData();
        formData.append('action', 'aui_upload_images');
        formData.append('nonce', aui_ajax.nonce);

        // Add files to FormData
        console.log('AUI Debug - selectedFiles array:', selectedFiles);
        console.log('AUI Debug - selectedFiles count:', selectedFiles.length);

        selectedFiles.forEach(function(file, index) {
            console.log('AUI Debug - Adding file #' + index + ':', file.name, 'size:', file.size);
            formData.append('image_files[]', file);
        });

        // Make AJAX request
        $.ajax({
            url: aui_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                clearInterval(progressInterval);
                showProgress(selectedFiles.length, selectedFiles.length); // 100%

                if (response.success) {
                    handleUploadSuccess(response.data);
                } else {
                    handleUploadError(response.data || 'Unknown error');
                }
            },
            error: function(xhr, status, error) {
                clearInterval(progressInterval);
                handleUploadError('AJAX Error: ' + error);
            },
            complete: function() {
                clearInterval(progressInterval);
                uploadInProgress = false;
                updateSubmitButton();
                $('button[type="submit"]').removeClass('loading');

                // Delay hiding progress to show 100% briefly
                setTimeout(function() {
                    hideProgress();
                }, 1000);
            }
        });
    }
    
    /**
     * Handle successful upload response
     */
    function handleUploadSuccess(data) {
        // Reset upload state
        uploadInProgress = false;

        uploadResults = data.results || [];
        successfulFiles = data.successful_uploads || [];

        // Debug log
        console.log('Upload response data:', data);
        console.log('Upload results:', uploadResults);
        console.log('Successful files:', successfulFiles);

        // Check for duplicates in results
        var filenames = uploadResults.map(function(r) { return r.filename; });
        var uniqueFilenames = [...new Set(filenames)];
        console.log('All filenames:', filenames);
        console.log('Unique filenames:', uniqueFilenames);
        console.log('Has duplicates:', filenames.length !== uniqueFilenames.length);

        // Check unique IDs
        var uniqueIds = uploadResults.map(function(r) { return r.unique_id || 'no_id'; });
        console.log('Unique IDs:', uniqueIds);

        $('#aui-upload-status').html('<span class="aui-success">' + data.message + '</span>');

        // Auto-remove successfully uploaded files from selection (if enabled)
        if ($('#aui-auto-remove-uploaded').is(':checked')) {
            removeUploadedFilesFromSelection(data.results);
        }

        // Update UI state
        updateSubmitButton();
        $('button[type="submit"]').removeClass('loading');
        hideProgress();

        displayResults(data);

        // Reset form for next upload
        resetFormForNextUpload();
    }

    /**
     * Reset form state for next upload
     */
    function resetFormForNextUpload() {
        // Clear file input
        $('#aui-image-files').val('');

        // Reset drop zone state
        $('#aui-drop-zone').removeClass('dragover has-files');

        // Clear any temporary UI states
        $('.aui-file-preview').remove();

        // Ensure upload button is enabled
        updateSubmitButton();

        console.log('Form reset for next upload');
    }

    /**
     * Handle upload error
     */
    function handleUploadError(error) {
        // Reset upload state
        uploadInProgress = false;

        $('#aui-upload-status').html('<span class="aui-error">Error: ' + error + '</span>');

        // Update UI state
        updateSubmitButton();
        $('button[type="submit"]').removeClass('loading');
        hideProgress();
    }
    
    /**
     * Display upload results
     */
    function displayResults(data) {
        var html = '';
        
        // Summary
        if (data.summary) {
            html += '<div class="aui-summary">';
            html += '<h4>Tổng kết</h4>';
            html += '<div class="aui-summary-stats">';
            html += '<div class="aui-summary-stat total">';
            html += '<span class="number">' + data.summary.total + '</span>';
            html += '<span class="label">Tổng</span>';
            html += '</div>';
            html += '<div class="aui-summary-stat success">';
            html += '<span class="number">' + data.summary.successful + '</span>';
            html += '<span class="label">Successful</span>';
            html += '</div>';
            html += '<div class="aui-summary-stat error">';
            html += '<span class="number">' + data.summary.failed + '</span>';
            html += '<span class="label">Thất bại</span>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
        }

        // All Links section - collect from all successful results
        var allLinks = [];
        console.log('Checking for links in uploadResults:', uploadResults);
        console.log('Upload results array:', uploadResults);

        if (uploadResults && uploadResults.length > 0) {
            uploadResults.forEach(function(result, index) {
                console.log('Processing result #' + index + ':', result);
                if (result.success && result.response && result.response.file_link) {
                    allLinks.push(result.response.file_link);
                    console.log('Added link #' + index + ':', result.response.file_link);
                }
            });
        }

        console.log('Final allLinks array:', allLinks);

        // Show All Links section prominently at the top
        if (allLinks.length > 0) {
            html += '<div class="aui-all-links" style="background: #e8f5e8; border: 2px solid #4caf50; margin-bottom: 20px;">';
            html += '<h4 style="color: #2e7d32; margin-top: 0;">📋 All Links (' + allLinks.length + ' links)</h4>';
            html += '<div class="aui-links-container">';
            html += '<textarea id="aui-all-links-textarea" readonly style="height: 120px; font-family: monospace; font-size: 12px;">' + allLinks.join('\n') + '</textarea>';
            html += '<div class="aui-links-actions" style="margin-top: 10px;">';
            html += '<button id="aui-copy-all-links" class="button button-primary" title="Copy tất cả link vào clipboard">📋 Copy toàn bộ</button>';
            html += '<button id="aui-select-all-links" class="button button-secondary" title="Chọn tất cả text">🔍 Select All</button>';
            html += '<span class="description" style="margin-left: 10px;">Click vào textarea để chọn tất cả link</span>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            console.log('All Links HTML added to output');
        } else {
            console.log('No links found to display');
            // Show debug info
            html += '<div class="aui-all-links" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin-bottom: 20px;">';
            html += '<h4>📋 All Links (0 links)</h4>';
            html += '<p>Debug: No links found. Check console for details.</p>';
            html += '<p>Upload results length: ' + (uploadResults ? uploadResults.length : 'undefined') + '</p>';
            html += '</div>';
        }
        
        // Results - Group by group_index for better display
        if (uploadResults.length > 0) {
            console.log('Rendering results. Total count:', uploadResults.length);
            var currentGroup = -1;
            var groupCount = 0;

            uploadResults.forEach(function(result, index) {
                console.log('Rendering result #' + index + ':', result.filename, 'ID:', result.unique_id, 'Group:', result.group_index);
                // Check if we're starting a new group
                if (result.group_index !== currentGroup) {
                    currentGroup = result.group_index;
                    groupCount++;

                    // Add group header if more than 1 file in group
                    if (result.group_size > 1) {
                        html += '<div class="aui-group-header">';
                        html += '<h5>📁 Nhóm ' + groupCount + ' (' + result.group_size + ' files) - ' + result.domain + '</h5>';
                        html += '</div>';
                    }
                }

                var groupClass = result.group_size > 1 ? 'aui-result-grouped' : '';
                html += '<div class="aui-result-item ' + (result.success ? 'aui-result-success' : 'aui-result-error') + ' ' + groupClass + '">';

                // Header
                html += '<div class="aui-result-header">';
                html += '<span>' + result.filename + '</span>';
                if (result.group_size === 1) {
                    html += '<span class="aui-result-domain">' + result.domain + '</span>';
                }
                html += '</div>';

                if (result.success) {
                    // Success result
                    html += '<div>✓ Upload successful';
                    if (result.attempt > 1) {
                        html += ' (after ' + result.attempt + ' attempts)';
                    }
                    if (result.temp_file_deleted) {
                        html += ' - File VPS đã xóa';
                    }
                    html += '</div>';

                    if (result.response) {
                        html += '<div class="aui-result-links">';
                        if (result.response.file_link) {
                            html += '<a href="' + result.response.file_link + '" target="_blank">File Link</a>';
                        }
                        if (result.response.direct_link) {
                            html += '<a href="' + result.response.direct_link + '" target="_blank">Direct Link</a>';
                        }
                        html += '</div>';

                        // Show file info
                        html += '<div class="aui-file-info-result">';
                        html += '<small>File ID: ' + (result.response.file_id || 'N/A') + '</small>';
                        html += '</div>';
                    }
                } else {
                    // Error result
                    html += '<div>✗ Upload failed';
                    if (result.attempts > 1) {
                        html += ' (tried ' + result.attempts + ' times)';
                    }
                    html += '</div>';

                    if (result.error) {
                        html += '<div class="aui-result-error-msg">' + escapeHtml(result.error) + '</div>';
                    }
                }

                html += '</div>';
            });
        }
        
        $('#aui-results-content').html(html);
        $('#aui-upload-results').show();

        // Show delete button if there are successful uploads
        if (successfulFiles.length > 0) {
            $('#aui-delete-files').show();
        } else {
            $('#aui-delete-files').hide();
        }

        // Add event handlers for copy buttons - delay to ensure DOM is ready
        setTimeout(function() {
            setupCopyHandlers();
            console.log('Copy handlers setup completed');
        }, 100);
    }
    
    /**
     * Handle delete uploaded files
     */
    $('#aui-delete-files').on('click', function() {
        if (successfulFiles.length === 0) {
            alert('No files to delete!');
            return;
        }

        if (!confirm('Are you sure you want to delete ' + successfulFiles.length + ' successfully uploaded files?')) {
            return;
        }
        
        var filePaths = successfulFiles.map(function(file) {
            return file.original_path; // Use file path instead of URL
        });
        
        $(this).prop('disabled', true).text('Deleting...');
        
        $.ajax({
            url: aui_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'aui_delete_uploaded_files',
                nonce: aui_ajax.nonce,
                file_paths: filePaths
            },
            success: function(response) {
                if (response.success) {
                    alert('Deleted ' + response.data.deleted_count + '/' + response.data.total_files + ' files!');
                    $('#aui-delete-files').hide();
                } else {
                    alert('Error deleting files: ' + (response.data || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                alert('AJAX Error: ' + error);
            },
            complete: function() {
                $('#aui-delete-files').prop('disabled', false).text('Delete successfully uploaded files');
            }
        });
    });
    
    /**
     * Show progress bar
     */
    function showProgress(current, total) {
        var percent = total > 0 ? Math.round((current / total) * 100) : 0;

        if ($('.aui-progress-container').length === 0) {
            var progressHtml = '<div class="aui-progress-container">' +
                '<div class="aui-progress-header">' +
                '<h4>🚀 Uploading images...</h4>' +
                '<span class="aui-progress-counter">' + current + ' / ' + total + ' files</span>' +
                '</div>' +
                '<div class="aui-progress">' +
                '<div class="aui-progress-bar">' +
                '<span class="aui-progress-text">0%</span>' +
                '</div>' +
                '</div>' +
                '<div class="aui-progress-status">Preparing...</div>' +
                '</div>';
            $('#aui-upload-form').after(progressHtml);
        }

        // Update progress
        $('.aui-progress-bar').css('width', percent + '%');
        $('.aui-progress-text').text(percent + '%');
        $('.aui-progress-counter').text(current + ' / ' + total + ' files');

        // Update status text
        if (percent === 0) {
            $('.aui-progress-status').text('Preparing upload...');
        } else if (percent === 100) {
            $('.aui-progress-status').text('✅ Upload completed!');
        } else {
            $('.aui-progress-status').text('⏳ Uploading file ' + (current + 1) + '...');
        }
    }
    
    /**
     * Start progress simulation
     */
    function startProgressSimulation(totalFiles) {
        var currentProgress = 0;
        var targetProgress = 90; // Don't go to 100% until actual completion
        var increment = targetProgress / (totalFiles * 2); // Slower progress

        return setInterval(function() {
            if (currentProgress < targetProgress) {
                currentProgress += increment;
                var simulatedCurrent = Math.floor((currentProgress / 100) * totalFiles);
                showProgress(simulatedCurrent, totalFiles);
            }
        }, 200); // Update every 200ms
    }

    /**
     * Hide progress bar
     */
    function hideProgress() {
        $('.aui-progress-container').fadeOut(300, function() {
            $(this).remove();
        });
    }
    
    /**
     * Validate URL
     */
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
    
    /**
     * Escape HTML
     */
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    /**
     * Format file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Setup copy handlers for all links
     */
    function setupCopyHandlers() {
        // Copy all links button
        $('#aui-copy-all-links').off('click').on('click', function() {
            var $textarea = $('#aui-all-links-textarea');
            var $button = $(this);
            var text = $textarea.val();

            if (!text) {
                alert('No links to copy!');
                return;
            }

            var originalText = $button.text();
            $button.prop('disabled', true);

            // Try modern Clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(function() {
                    showCopySuccess($button, originalText);
                }).catch(function(err) {
                    console.log('Clipboard API failed:', err);
                    fallbackCopy($textarea, $button, originalText);
                });
            } else {
                // Fallback to older method
                fallbackCopy($textarea, $button, originalText);
            }
        });

        // Select all links button
        $('#aui-select-all-links').off('click').on('click', function() {
            var $textarea = $('#aui-all-links-textarea');
            if ($textarea.length > 0) {
                $textarea.select();
                $textarea.focus();
            }
        });

        // Auto-select on textarea click
        $('#aui-all-links-textarea').off('click').on('click', function() {
            $(this).select();
        });
    }

    /**
     * Show copy success feedback
     */
    function showCopySuccess($button, originalText) {
        $button.text('✓ Đã copy!').addClass('copied');
        setTimeout(function() {
            $button.text(originalText).removeClass('copied').prop('disabled', false);
        }, 2000);
    }

    /**
     * Fallback copy method for older browsers
     */
    function fallbackCopy($textarea, $button, originalText) {
        try {
            $textarea.select();
            $textarea[0].setSelectionRange(0, 99999); // For mobile devices

            var successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess($button, originalText);
            } else {
                $button.prop('disabled', false);
                alert('Cannot copy automatically. Please select text and press Ctrl+C.');
            }
        } catch (err) {
            $button.prop('disabled', false);
            $textarea.select();
            alert('Please select text and press Ctrl+C (or Cmd+C) to copy.');
        }
    }
    
    /**
     * Auto-resize textarea
     */
    $('#aui-image-urls').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    /**
     * Domain management (for config page)
     */
    if ($('#aui-add-domain').length > 0) {
        // Add domain
        $('#aui-add-domain').click(function() {
            var newDomain = '<div class="aui-domain-item">' +
                '<input type="url" name="domains[]" value="" placeholder="https://example.com" />' +
                '<button type="button" class="button aui-remove-domain">Xóa</button>' +
                '</div>';
            $('#aui-domains-list').append(newDomain);
        });
        
        // Remove domain
        $(document).on('click', '.aui-remove-domain', function() {
            if ($('.aui-domain-item').length > 1) {
                $(this).parent().remove();
            } else {
                alert('Must have at least one domain!');
            }
        });
        
        // Validate domain on input
        $(document).on('input', 'input[name="domains[]"]', function() {
            var $input = $(this);
            var url = $input.val().trim();
            
            if (url && !isValidUrl(url)) {
                $input.css('border-color', '#dc3232');
            } else {
                $input.css('border-color', '');
            }
        });
    }
    
    /**
     * Form validation
     */
    $('form').on('submit', function() {
        var isValid = true;
        
        // Validate domain URLs
        $('input[name="domains[]"]').each(function() {
            var url = $(this).val().trim();
            if (url && !isValidUrl(url)) {
                $(this).css('border-color', '#dc3232');
                isValid = false;
            }
        });
        
        if (!isValid) {
            alert('Please check the domain URLs!');
            return false;
        }
        
        return true;
    });
    
});
